INFO:2025-05-31 17:44:43,537:MainThread:Dhan.py  started system
INFO:2025-05-31 17:44:43,538:MainThread:STARTED THE PROGRAM
INFO:2025-05-31 17:45:23,669:MainThread:Dhan.py  started system
INFO:2025-05-31 17:45:23,669:MainThread:STARTED THE PROGRAM
ERROR:2025-05-31 17:45:29,458:MainThread:Exception in Getting OHLC data as {'status': 'failure', 'remarks': {'error_code': 'DH-901', 'error_type': 'Invalid_Authentication', 'error_message': 'Client ID or user generated access token is invalid or expired.'}, 'data': {'errorType': 'Invalid_Authentication', 'errorCode': 'DH-901', 'errorMessage': 'Client ID or user generated access token is invalid or expired.'}}
Traceback (most recent call last):
  File "/Volumes/Kaundinya1TB/docs/Stocks and Shares/Dhan_Examples/ema-crossover-analysis/.venv/lib/python3.11/site-packages/Dhan_Tradehull/Dhan_Tradehull.py", line 464, in get_historical_data
    raise Exception(ohlc)
Exception: {'status': 'failure', 'remarks': {'error_code': 'DH-901', 'error_type': 'Invalid_Authentication', 'error_message': 'Client ID or user generated access token is invalid or expired.'}, 'data': {'errorType': 'Invalid_Authentication', 'errorCode': 'DH-901', 'errorMessage': 'Client ID or user generated access token is invalid or expired.'}}
