"""
EMA Crossover Analysis for Minute-based Candle Data
===================================================

This script analyzes 1-minute candle data for EMA 5 and EMA 10 crossovers.
- Upper crossover (EMA 5 crosses above EMA 10) = BUY signal
- Lower crossover (EMA 5 crosses below EMA 10) = SELL signal

Outputs:
1. candle_data.csv - All 1-minute candle close price data
2. signals.csv - Buy and sell signal data with timestamps

Author: Generated for Dhan Trading Analysis
"""

import pandas as pd
import numpy as np
import datetime
import os
from typing import Tuple, Optional
import warnings

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')

try:
    from Dhan_Tradehull import Tradehull
    DHAN_AVAILABLE = True
except ImportError:
    DHAN_AVAILABLE = False
    print("Warning: Dhan_Tradehull not available. Using sample data generation.")


class EMACrossoverAnalyzer:
    """
    EMA Crossover Analysis class for processing minute-based candle data
    """

    def __init__(self, client_code: Optional[str] = None, token_id: Optional[str] = None):
        """
        Initialize the EMA Crossover Analyzer

        Args:
            client_code: Dhan client code (optional)
            token_id: Dhan token ID (optional)
        """
        self.client_code = client_code
        self.token_id = token_id
        self.tsl = None

        if DHAN_AVAILABLE and client_code and token_id:
            try:
                self.tsl = Tradehull(client_code, token_id)
                print("✓ Dhan API connection established")
            except Exception as e:
                print(f"✗ Failed to connect to Dhan API: {e}")
                self.tsl = None

        # Create output directory
        self.output_dir = "output"
        os.makedirs(self.output_dir, exist_ok=True)

    def generate_sample_data(self, symbol: str = "NIFTY", days: int = 5) -> pd.DataFrame:
        """
        Generate sample 1-minute candle data for testing

        Args:
            symbol: Trading symbol
            days: Number of days of data to generate

        Returns:
            DataFrame with OHLCV data
        """
        print(f"📊 Generating sample data for {symbol} ({days} days)")

        # Generate timestamps for market hours (9:15 AM to 3:30 PM)
        timestamps = []
        base_date = datetime.datetime.now() - datetime.timedelta(days=days)

        for day in range(days):
            current_date = base_date + datetime.timedelta(days=day)
            # Skip weekends
            if current_date.weekday() >= 5:
                continue

            # Market hours: 9:15 AM to 3:30 PM (375 minutes)
            start_time = current_date.replace(hour=9, minute=15, second=0, microsecond=0)
            for minute in range(375):  # 375 minutes in trading session
                timestamps.append(start_time + datetime.timedelta(minutes=minute))

        # Generate realistic price data with trend and volatility
        np.random.seed(42)  # For reproducible results
        base_price = 24000  # Starting price for NIFTY

        data = []
        current_price = base_price

        for i, timestamp in enumerate(timestamps):
            # Add some trend and random walk
            trend = 0.001 * np.sin(i / 100)  # Slight trending component
            volatility = np.random.normal(0, 0.002)  # Random volatility

            price_change = trend + volatility
            current_price = current_price * (1 + price_change)

            # Generate OHLC data
            high = current_price * (1 + abs(np.random.normal(0, 0.001)))
            low = current_price * (1 - abs(np.random.normal(0, 0.001)))
            open_price = current_price + np.random.normal(0, current_price * 0.0005)
            close_price = current_price
            volume = np.random.randint(1000, 10000)

            data.append({
                'timestamp': timestamp.strftime('%Y-%m-%d %H:%M:%S+05:30'),
                'open': round(open_price, 2),
                'high': round(high, 2),
                'low': round(low, 2),
                'close': round(close_price, 2),
                'volume': volume
            })

        df = pd.DataFrame(data)
        print(f"✓ Generated {len(df)} data points")
        return df

    def fetch_live_data(self, symbol: str = "NIFTY", exchange: str = "INDEX") -> Optional[pd.DataFrame]:
        """
        Fetch live 1-minute candle data from Dhan API

        Args:
            symbol: Trading symbol
            exchange: Exchange name

        Returns:
            DataFrame with OHLCV data or None if failed
        """
        if not self.tsl:
            print("✗ Dhan API not available")
            return None

        try:
            print(f"📡 Fetching live data for {symbol} from {exchange}")
            data = self.tsl.get_historical_data(
                tradingsymbol=symbol,
                exchange=exchange,
                timeframe="1"  # 1-minute timeframe
            )
            print(f"✓ Fetched {len(data)} data points from Dhan API")
            return data
        except Exception as e:
            print(f"✗ Failed to fetch data from Dhan API: {e}")
            return None

    def calculate_ema(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate EMA 5 and EMA 10 for the given data

        Args:
            data: DataFrame with OHLCV data

        Returns:
            DataFrame with EMA columns added
        """
        print("📈 Calculating EMA 5 and EMA 10")

        # Make a copy to avoid modifying original data
        df = data.copy()

        # Calculate EMAs manually
        df['ema_5'] = self._calculate_ema_manual(df['close'], 5)
        df['ema_10'] = self._calculate_ema_manual(df['close'], 10)

        # Remove rows with NaN values (first few rows won't have EMA values)
        df = df.dropna()

        print(f"✓ EMA calculations completed. {len(df)} valid data points.")
        return df

    def _calculate_ema_manual(self, prices: pd.Series, period: int) -> pd.Series:
        """
        Calculate Exponential Moving Average manually

        Args:
            prices: Series of prices
            period: EMA period

        Returns:
            Series with EMA values
        """
        # Calculate smoothing factor (alpha)
        alpha = 2 / (period + 1)

        # Initialize EMA series
        ema = pd.Series(index=prices.index, dtype=float)

        # First EMA value is the first price
        ema.iloc[0] = prices.iloc[0]

        # Calculate subsequent EMA values
        for i in range(1, len(prices)):
            ema.iloc[i] = alpha * prices.iloc[i] + (1 - alpha) * ema.iloc[i-1]

        return ema

    def detect_crossovers(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Detect EMA crossovers and generate buy/sell signals

        Args:
            data: DataFrame with EMA columns

        Returns:
            DataFrame with crossover signals
        """
        print("🔍 Detecting EMA crossovers")

        df = data.copy()

        # Calculate the difference between EMA 5 and EMA 10
        df['ema_diff'] = df['ema_5'] - df['ema_10']

        # Detect crossovers by looking at sign changes
        df['prev_ema_diff'] = df['ema_diff'].shift(1)

        # Buy signal: EMA 5 crosses above EMA 10 (positive crossover)
        df['buy_signal'] = (
            (df['ema_diff'] > 0) &
            (df['prev_ema_diff'] <= 0)
        )

        # Sell signal: EMA 5 crosses below EMA 10 (negative crossover)
        df['sell_signal'] = (
            (df['ema_diff'] < 0) &
            (df['prev_ema_diff'] >= 0)
        )

        # Create signal column
        df['signal'] = 'HOLD'
        df.loc[df['buy_signal'], 'signal'] = 'BUY'
        df.loc[df['sell_signal'], 'signal'] = 'SELL'

        # Count signals
        buy_count = df['buy_signal'].sum()
        sell_count = df['sell_signal'].sum()

        print(f"✓ Crossover detection completed:")
        print(f"  📈 Buy signals: {buy_count}")
        print(f"  📉 Sell signals: {sell_count}")

        return df

    def save_candle_data(self, data: pd.DataFrame, filename: str = "candle_data.csv") -> str:
        """
        Save all 1-minute candle close price data to CSV

        Args:
            data: DataFrame with candle data
            filename: Output filename

        Returns:
            Full path to saved file
        """
        filepath = os.path.join(self.output_dir, filename)

        # Select relevant columns for candle data
        candle_df = data[['timestamp', 'open', 'high', 'low', 'close', 'volume']].copy()

        # Save to CSV
        candle_df.to_csv(filepath, index=False)

        print(f"💾 Candle data saved to: {filepath}")
        print(f"   📊 Records: {len(candle_df)}")

        return filepath

    def save_signals_data(self, data: pd.DataFrame, filename: str = "signals.csv") -> str:
        """
        Save buy and sell signal data to CSV

        Args:
            data: DataFrame with signal data
            filename: Output filename

        Returns:
            Full path to saved file
        """
        filepath = os.path.join(self.output_dir, filename)

        # Filter only rows with buy or sell signals
        signals_df = data[data['signal'].isin(['BUY', 'SELL'])].copy()

        # Select relevant columns for signals
        signals_df = signals_df[[
            'timestamp', 'close', 'ema_5', 'ema_10', 'signal'
        ]].copy()

        # Add additional information
        signals_df['price'] = signals_df['close']
        signals_df['ema_5_value'] = signals_df['ema_5'].round(2)
        signals_df['ema_10_value'] = signals_df['ema_10'].round(2)

        # Reorder columns
        signals_df = signals_df[[
            'timestamp', 'signal', 'price', 'ema_5_value', 'ema_10_value'
        ]]

        # Save to CSV
        signals_df.to_csv(filepath, index=False)

        print(f"💾 Signals data saved to: {filepath}")
        print(f"   📊 Signal records: {len(signals_df)}")

        return filepath

    def run_analysis(self, symbol: str = "NIFTY", exchange: str = "INDEX",
                    use_sample_data: bool = False, days: int = 5) -> Tuple[str, str]:
        """
        Run complete EMA crossover analysis

        Args:
            symbol: Trading symbol to analyze
            exchange: Exchange name
            use_sample_data: Whether to use sample data instead of live data
            days: Number of days for sample data generation

        Returns:
            Tuple of (candle_data_file_path, signals_file_path)
        """
        print("🚀 Starting EMA Crossover Analysis")
        print("=" * 50)

        # Step 1: Get data
        if use_sample_data or not self.tsl:
            data = self.generate_sample_data(symbol, days)
        else:
            data = self.fetch_live_data(symbol, exchange)
            if data is None:
                print("⚠️  Falling back to sample data")
                data = self.generate_sample_data(symbol, days)

        # Step 2: Calculate EMAs
        data_with_ema = self.calculate_ema(data)

        # Step 3: Detect crossovers
        data_with_signals = self.detect_crossovers(data_with_ema)

        # Step 4: Save data to CSV files
        candle_file = self.save_candle_data(data_with_signals)
        signals_file = self.save_signals_data(data_with_signals)

        # Step 5: Display summary
        self.display_summary(data_with_signals)

        print("=" * 50)
        print("✅ Analysis completed successfully!")

        return candle_file, signals_file

    def display_summary(self, data: pd.DataFrame) -> None:
        """
        Display analysis summary

        Args:
            data: DataFrame with complete analysis data
        """
        print("\n📊 ANALYSIS SUMMARY")
        print("-" * 30)

        total_records = len(data)
        buy_signals = data['buy_signal'].sum()
        sell_signals = data['sell_signal'].sum()

        print(f"Total data points: {total_records}")
        print(f"Buy signals: {buy_signals}")
        print(f"Sell signals: {sell_signals}")
        print(f"Signal frequency: {((buy_signals + sell_signals) / total_records * 100):.2f}%")

        if buy_signals > 0 or sell_signals > 0:
            print("\n🎯 RECENT SIGNALS:")
            recent_signals = data[data['signal'].isin(['BUY', 'SELL'])].tail(5)
            for _, row in recent_signals.iterrows():
                signal_emoji = "📈" if row['signal'] == 'BUY' else "📉"
                print(f"  {signal_emoji} {row['timestamp']}: {row['signal']} at {row['close']:.2f}")


def main():
    """
    Main function to run the EMA crossover analysis
    """
    print("🎯 EMA Crossover Analysis Tool")
    print("=" * 50)

    # Configuration - Update these with your Dhan credentials if available
    CLIENT_CODE = None  # Replace with your Dhan client code
    TOKEN_ID = None     # Replace with your Dhan token ID

    # Analysis parameters
    SYMBOL = "NIFTY"
    EXCHANGE = "INDEX"
    USE_SAMPLE_DATA = True  # Set to False to use live data (requires valid credentials)
    SAMPLE_DAYS = 5

    try:
        # Initialize analyzer
        analyzer = EMACrossoverAnalyzer(CLIENT_CODE, TOKEN_ID)

        # Run analysis
        candle_file, signals_file = analyzer.run_analysis(
            symbol=SYMBOL,
            exchange=EXCHANGE,
            use_sample_data=USE_SAMPLE_DATA,
            days=SAMPLE_DAYS
        )

        print(f"\n📁 Output files created:")
        print(f"  📊 Candle data: {candle_file}")
        print(f"  🎯 Signals data: {signals_file}")

    except Exception as e:
        print(f"❌ Error during analysis: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
