"""
EMA Crossover Analysis for Minute-based Candle Data
===================================================

This script analyzes 1-minute candle data for EMA 5 and EMA 10 crossovers.
- Upper crossover (EMA 5 crosses above EMA 10) = BUY signal
- Lower crossover (EMA 5 crosses below EMA 10) = SELL signal

Outputs:
1. candle_data.csv - All 1-minute candle close price data
2. signals.csv - Buy and sell signal data with timestamps

Author: Generated for Dhan Trading Analysis
"""

import pandas as pd
import numpy as np
import datetime
import os
from typing import Tuple, Optional
import warnings

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')

# Import configuration
try:
    from config import get_config, validate_config, print_config
    CONFIG_AVAILABLE = True
except ImportError:
    CONFIG_AVAILABLE = False
    print("⚠️  config.py not found, using default configuration")

try:
    from Dhan_Tradehull import Tradehull
    DHAN_AVAILABLE = True
except ImportError:
    DHAN_AVAILABLE = False
    print("Warning: Dhan_Tradehull not available. Using sample data generation.")


class EMACrossoverAnalyzer:
    """
    EMA Crossover Analysis class for processing minute-based candle data
    """

    def __init__(self, client_code: Optional[str] = None, token_id: Optional[str] = None):
        """
        Initialize the EMA Crossover Analyzer

        Args:
            client_code: Dhan client code (optional)
            token_id: Dhan token ID (optional)
        """
        self.client_code = client_code
        self.token_id = token_id
        self.tsl = None

        if DHAN_AVAILABLE and client_code and token_id:
            try:
                self.tsl = Tradehull(client_code, token_id)
                print("✓ Dhan API connection established")
            except Exception as e:
                print(f"✗ Failed to connect to Dhan API: {e}")
                self.tsl = None

        # Create output directory
        self.output_dir = "output"
        os.makedirs(self.output_dir, exist_ok=True)

    def generate_sample_data(self, symbol: str = "NIFTY", days: int = 5) -> pd.DataFrame:
        """
        Generate sample 1-minute candle data for testing

        Args:
            symbol: Trading symbol
            days: Number of days of data to generate

        Returns:
            DataFrame with OHLCV data
        """
        print(f"📊 Generating sample data for {symbol} ({days} days)")

        # Generate timestamps for market hours (9:15 AM to 3:30 PM)
        timestamps = []
        base_date = datetime.datetime.now() - datetime.timedelta(days=days)

        for day in range(days):
            current_date = base_date + datetime.timedelta(days=day)
            # Skip weekends
            if current_date.weekday() >= 5:
                continue

            # Market hours: 9:15 AM to 3:30 PM (375 minutes)
            start_time = current_date.replace(hour=9, minute=15, second=0, microsecond=0)
            for minute in range(375):  # 375 minutes in trading session
                timestamps.append(start_time + datetime.timedelta(minutes=minute))

        # Generate realistic price data with trend and volatility
        np.random.seed(42)  # For reproducible results
        base_price = 24000  # Starting price for NIFTY

        data = []
        current_price = base_price

        for i, timestamp in enumerate(timestamps):
            # Add some trend and random walk
            trend = 0.001 * np.sin(i / 100)  # Slight trending component
            volatility = np.random.normal(0, 0.002)  # Random volatility

            price_change = trend + volatility
            current_price = current_price * (1 + price_change)

            # Generate OHLC data
            high = current_price * (1 + abs(np.random.normal(0, 0.001)))
            low = current_price * (1 - abs(np.random.normal(0, 0.001)))
            open_price = current_price + np.random.normal(0, current_price * 0.0005)
            close_price = current_price
            volume = np.random.randint(1000, 10000)

            data.append({
                'timestamp': timestamp.strftime('%Y-%m-%d %H:%M:%S+05:30'),
                'open': round(open_price, 2),
                'high': round(high, 2),
                'low': round(low, 2),
                'close': round(close_price, 2),
                'volume': volume
            })

        df = pd.DataFrame(data)
        print(f"✓ Generated {len(df)} data points")
        return df

    def fetch_live_data(self, symbol: str = "NIFTY", exchange: str = "INDEX", days: int = 2) -> Optional[pd.DataFrame]:
        """
        Fetch live 1-minute candle data from Dhan API for specified days

        Args:
            symbol: Trading symbol
            exchange: Exchange name
            days: Number of trading days to fetch

        Returns:
            DataFrame with OHLCV data or None if failed
        """
        if not self.tsl:
            print("✗ Dhan API not available")
            return None

        try:
            print(f"📡 Fetching {days} days of live 1-minute data for {symbol} from {exchange}")

            # Fetch historical data from Dhan API (automatically gets last 365 days)
            data = self.tsl.get_historical_data(
                tradingsymbol=symbol,
                exchange=exchange,
                timeframe="1"  # 1-minute timeframe
            )

            if data is None or len(data) == 0:
                print("✗ No data received from Dhan API")
                return None

            # Convert to DataFrame if it's not already
            if not isinstance(data, pd.DataFrame):
                df = pd.DataFrame(data)
            else:
                df = data.copy()

            # Filter for market hours only (9:15 AM to 3:30 PM IST)
            df = self._filter_market_hours(df)

            # Keep only the last 'days' trading days
            df = self._keep_last_trading_days(df, days)

            print(f"✓ Fetched {len(df)} data points from Dhan API")
            print(f"📊 Data range: {df['timestamp'].iloc[0]} to {df['timestamp'].iloc[-1]}")

            return df

        except Exception as e:
            print(f"✗ Failed to fetch data from Dhan API: {e}")
            import traceback
            traceback.print_exc()
            return None

    def _filter_market_hours(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Filter data to include only market hours (9:15 AM to 3:30 PM IST)

        Args:
            data: DataFrame with timestamp column

        Returns:
            Filtered DataFrame
        """
        df = data.copy()

        # Convert timestamp to datetime if it's not already
        if 'timestamp' in df.columns:
            df['timestamp_dt'] = pd.to_datetime(df['timestamp'])
        else:
            # Try common timestamp column names
            timestamp_cols = ['time', 'datetime', 'date']
            for col in timestamp_cols:
                if col in df.columns:
                    df['timestamp'] = df[col]
                    df['timestamp_dt'] = pd.to_datetime(df[col])
                    break

        # Filter for market hours (9:15 AM to 3:30 PM)
        market_start = 9 * 60 + 15  # 9:15 AM in minutes
        market_end = 15 * 60 + 30   # 3:30 PM in minutes

        df['hour_minute'] = df['timestamp_dt'].dt.hour * 60 + df['timestamp_dt'].dt.minute

        # Filter for market hours and weekdays only
        df_filtered = df[
            (df['hour_minute'] >= market_start) &
            (df['hour_minute'] <= market_end) &
            (df['timestamp_dt'].dt.weekday < 5)  # Monday=0, Friday=4
        ].copy()

        # Clean up temporary columns
        df_filtered = df_filtered.drop(['timestamp_dt', 'hour_minute'], axis=1)

        return df_filtered

    def _keep_last_trading_days(self, data: pd.DataFrame, days: int) -> pd.DataFrame:
        """
        Keep only the last N trading days of data

        Args:
            data: DataFrame with timestamp column
            days: Number of trading days to keep

        Returns:
            DataFrame with last N trading days
        """
        df = data.copy()

        # Convert timestamp to datetime
        df['timestamp_dt'] = pd.to_datetime(df['timestamp'])
        df['date'] = df['timestamp_dt'].dt.date

        # Get unique trading dates and sort them
        unique_dates = sorted(df['date'].unique(), reverse=True)

        # Take the last 'days' trading days
        last_days = unique_dates[:days]

        # Filter data for these days
        df_filtered = df[df['date'].isin(last_days)].copy()

        # Sort by timestamp
        df_filtered = df_filtered.sort_values('timestamp_dt')

        # Clean up temporary columns
        df_filtered = df_filtered.drop(['timestamp_dt', 'date'], axis=1)

        print(f"📅 Keeping data for {len(last_days)} trading days: {last_days}")

        return df_filtered

    def calculate_ema(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate EMA 5 and EMA 10 for the given data

        Args:
            data: DataFrame with OHLCV data

        Returns:
            DataFrame with EMA columns added
        """
        print("📈 Calculating EMA 5 and EMA 10")

        # Make a copy to avoid modifying original data
        df = data.copy()

        # Calculate EMAs manually
        df['ema_5'] = self._calculate_ema_manual(df['close'], 5)
        df['ema_10'] = self._calculate_ema_manual(df['close'], 10)

        # Remove rows with NaN values (first few rows won't have EMA values)
        df = df.dropna()

        print(f"✓ EMA calculations completed. {len(df)} valid data points.")
        return df

    def _calculate_ema_manual(self, prices: pd.Series, period: int) -> pd.Series:
        """
        Calculate Exponential Moving Average manually

        Args:
            prices: Series of prices
            period: EMA period

        Returns:
            Series with EMA values
        """
        # Calculate smoothing factor (alpha)
        alpha = 2 / (period + 1)

        # Initialize EMA series
        ema = pd.Series(index=prices.index, dtype=float)

        # First EMA value is the first price
        ema.iloc[0] = prices.iloc[0]

        # Calculate subsequent EMA values
        for i in range(1, len(prices)):
            ema.iloc[i] = alpha * prices.iloc[i] + (1 - alpha) * ema.iloc[i-1]

        return ema

    def detect_crossovers(self, data: pd.DataFrame, min_separation_minutes: int = 5,
                         confirmation_periods: int = 2) -> pd.DataFrame:
        """
        Detect EMA crossovers and generate buy/sell signals with improved accuracy

        Args:
            data: DataFrame with EMA columns
            min_separation_minutes: Minimum minutes between signals
            confirmation_periods: Number of periods to confirm crossover

        Returns:
            DataFrame with crossover signals
        """
        print("🔍 Detecting EMA crossovers with improved accuracy")

        df = data.copy()

        # Calculate the difference between EMA 5 and EMA 10
        df['ema_diff'] = df['ema_5'] - df['ema_10']

        # Calculate previous differences for confirmation
        df['prev_ema_diff'] = df['ema_diff'].shift(1)
        df['prev2_ema_diff'] = df['ema_diff'].shift(2)

        # Initialize signal columns
        df['buy_signal'] = False
        df['sell_signal'] = False
        df['signal'] = 'HOLD'
        df['raw_buy_signal'] = False  # Track all crossovers before filtering
        df['raw_sell_signal'] = False

        # More robust crossover detection with confirmation
        for i in range(confirmation_periods, len(df)):
            current_diff = df.iloc[i]['ema_diff']
            prev_diff = df.iloc[i-1]['ema_diff']
            prev2_diff = df.iloc[i-2]['ema_diff'] if i >= 2 else prev_diff

            # Buy signal: EMA 5 crosses above EMA 10 with confirmation
            # Current and previous period both show EMA5 > EMA10, and before that EMA5 <= EMA10
            if (current_diff > 0 and prev_diff > 0 and prev2_diff <= 0):
                df.iloc[i, df.columns.get_loc('raw_buy_signal')] = True
                df.iloc[i, df.columns.get_loc('buy_signal')] = True
                df.iloc[i, df.columns.get_loc('signal')] = 'BUY'

            # Sell signal: EMA 5 crosses below EMA 10 with confirmation
            # Current and previous period both show EMA5 < EMA10, and before that EMA5 >= EMA10
            elif (current_diff < 0 and prev_diff < 0 and prev2_diff >= 0):
                df.iloc[i, df.columns.get_loc('raw_sell_signal')] = True
                df.iloc[i, df.columns.get_loc('sell_signal')] = True
                df.iloc[i, df.columns.get_loc('signal')] = 'SELL'

        # Store raw signals count before filtering
        raw_buy_count = df['raw_buy_signal'].sum()
        raw_sell_count = df['raw_sell_signal'].sum()

        # Apply minimum separation filter (only affects buy_signal and sell_signal columns)
        df = self._apply_signal_separation(df, min_separation_minutes)

        # Create a comprehensive signal column that shows all crossovers
        df['all_crossovers'] = 'HOLD'
        df.loc[df['raw_buy_signal'], 'all_crossovers'] = 'BUY'
        df.loc[df['raw_sell_signal'], 'all_crossovers'] = 'SELL'

        # Count final signals
        buy_count = df['buy_signal'].sum()
        sell_count = df['sell_signal'].sum()

        print(f"✓ Crossover detection completed:")
        print(f"  📈 Buy signals (filtered): {buy_count}")
        print(f"  📉 Sell signals (filtered): {sell_count}")
        print(f"  🔍 Total crossovers detected: {raw_buy_count + raw_sell_count}")
        print(f"  📈 Raw buy crossovers: {raw_buy_count}")
        print(f"  📉 Raw sell crossovers: {raw_sell_count}")
        print(f"  ⏱️  Min separation: {min_separation_minutes} minutes")
        print(f"  ✅ Confirmation periods: {confirmation_periods}")

        return df

    def _apply_signal_separation(self, data: pd.DataFrame, min_minutes: int) -> pd.DataFrame:
        """
        Apply minimum separation between signals to avoid noise

        Args:
            data: DataFrame with signals
            min_minutes: Minimum minutes between signals

        Returns:
            DataFrame with filtered signals
        """
        df = data.copy()

        # Convert timestamp to datetime for calculation
        df['timestamp_dt'] = pd.to_datetime(df['timestamp'])

        # Track last signal time
        last_signal_time = None

        for i in range(len(df)):
            if df.iloc[i]['signal'] in ['BUY', 'SELL']:
                current_time = df.iloc[i]['timestamp_dt']

                # If this is not the first signal, check separation
                if last_signal_time is not None:
                    time_diff = (current_time - last_signal_time).total_seconds() / 60

                    # If too close to previous signal, remove this signal
                    if time_diff < min_minutes:
                        df.iloc[i, df.columns.get_loc('buy_signal')] = False
                        df.iloc[i, df.columns.get_loc('sell_signal')] = False
                        df.iloc[i, df.columns.get_loc('signal')] = 'HOLD'
                        continue

                # Update last signal time
                last_signal_time = current_time

        # Drop the temporary datetime column
        df = df.drop('timestamp_dt', axis=1)

        return df

    def save_candle_data(self, data: pd.DataFrame, filename: str = "candle_data.csv") -> str:
        """
        Save all 1-minute candle data with EMA values and crossover signals to CSV

        Args:
            data: DataFrame with candle data, EMA values, and signals
            filename: Output filename

        Returns:
            Full path to saved file
        """
        filepath = os.path.join(self.output_dir, filename)

        # Select relevant columns for enhanced candle data
        candle_df = data[[
            'timestamp', 'open', 'high', 'low', 'close', 'volume',
            'ema_5', 'ema_10', 'all_crossovers', 'signal'
        ]].copy()

        # Round EMA values for better readability
        candle_df['ema_5'] = candle_df['ema_5'].round(2)
        candle_df['ema_10'] = candle_df['ema_10'].round(2)

        # Add EMA difference for analysis
        candle_df['ema_diff'] = (candle_df['ema_5'] - candle_df['ema_10']).round(2)

        # Add crossover type for better understanding
        candle_df['crossover_type'] = candle_df['all_crossovers'].apply(
            lambda x: 'Bullish' if x == 'BUY' else ('Bearish' if x == 'SELL' else 'None')
        )

        # Add filtered signal indicator
        candle_df['filtered_signal'] = candle_df['signal'].apply(
            lambda x: 'Yes' if x in ['BUY', 'SELL'] else 'No'
        )

        # Reorder columns for better readability
        candle_df = candle_df[[
            'timestamp', 'open', 'high', 'low', 'close', 'volume',
            'ema_5', 'ema_10', 'ema_diff', 'all_crossovers', 'crossover_type',
            'filtered_signal'
        ]]

        # Save to CSV
        candle_df.to_csv(filepath, index=False)

        # Count crossovers in the data
        total_crossovers = len(candle_df[candle_df['all_crossovers'] != 'HOLD'])
        filtered_signals = len(candle_df[candle_df['filtered_signal'] == 'Yes'])

        print(f"💾 Enhanced candle data saved to: {filepath}")
        print(f"   📊 Total records: {len(candle_df)}")
        print(f"   📈 EMA 5 & EMA 10 values for each minute")
        print(f"   🎯 All crossover occurrences marked: {total_crossovers}")
        print(f"   ✅ Filtered signals (after separation): {filtered_signals}")
        print(f"   📊 Columns: OHLCV + EMA5 + EMA10 + EMA_Diff + Crossovers + Filter_Status")

        return filepath

    def save_signals_data(self, data: pd.DataFrame, filename: str = "signals.csv") -> str:
        """
        Save buy and sell signal data to CSV

        Args:
            data: DataFrame with signal data
            filename: Output filename

        Returns:
            Full path to saved file
        """
        filepath = os.path.join(self.output_dir, filename)

        # Filter only rows with buy or sell signals
        signals_df = data[data['signal'].isin(['BUY', 'SELL'])].copy()

        # Select relevant columns for signals
        signals_df = signals_df[[
            'timestamp', 'close', 'ema_5', 'ema_10', 'signal'
        ]].copy()

        # Add additional information
        signals_df['price'] = signals_df['close']
        signals_df['ema_5_value'] = signals_df['ema_5'].round(2)
        signals_df['ema_10_value'] = signals_df['ema_10'].round(2)

        # Reorder columns
        signals_df = signals_df[[
            'timestamp', 'signal', 'price', 'ema_5_value', 'ema_10_value'
        ]]

        # Save to CSV
        signals_df.to_csv(filepath, index=False)

        print(f"💾 Signals data saved to: {filepath}")
        print(f"   📊 Signal records: {len(signals_df)}")

        return filepath

    def validate_signals(self, data: pd.DataFrame) -> None:
        """
        Validate and display signal details for debugging

        Args:
            data: DataFrame with signals
        """
        signals = data[data['signal'].isin(['BUY', 'SELL'])].copy()

        if len(signals) == 0:
            print("⚠️  No signals found to validate")
            return

        print(f"\n🔍 SIGNAL VALIDATION ({len(signals)} signals)")
        print("-" * 80)

        for _, row in signals.iterrows():
            signal_type = row['signal']
            ema5 = row['ema_5']
            ema10 = row['ema_10']
            diff = ema5 - ema10

            # Validate signal logic
            if signal_type == 'BUY' and diff <= 0:
                status = "❌ INVALID"
            elif signal_type == 'SELL' and diff >= 0:
                status = "❌ INVALID"
            else:
                status = "✅ VALID"

            print(f"{status} {row['timestamp']}: {signal_type} at {row['close']:.2f}")
            print(f"      EMA5: {ema5:.2f}, EMA10: {ema10:.2f}, Diff: {diff:.2f}")

    def run_analysis(self, symbol: str = "NIFTY", exchange: str = "INDEX",
                    use_sample_data: bool = False, days: int = 5,
                    min_separation: int = 5, confirmation_periods: int = 2) -> Tuple[str, str]:
        """
        Run complete EMA crossover analysis

        Args:
            symbol: Trading symbol to analyze
            exchange: Exchange name
            use_sample_data: Whether to use sample data instead of live data
            days: Number of days for sample data generation
            min_separation: Minimum minutes between signals
            confirmation_periods: Number of periods to confirm crossover

        Returns:
            Tuple of (candle_data_file_path, signals_file_path)
        """
        print("🚀 Starting EMA Crossover Analysis")
        print("=" * 50)

        # Step 1: Get data
        if use_sample_data:
            print("🧪 Using sample data as requested")
            data = self.generate_sample_data(symbol, days)
        elif not self.tsl:
            print("⚠️  Dhan API not configured, falling back to sample data")
            data = self.generate_sample_data(symbol, days)
        else:
            print("🔌 Attempting to fetch real data from Dhan API")
            data = self.fetch_live_data(symbol, exchange, days)
            if data is None:
                print("⚠️  Failed to fetch real data, falling back to sample data")
                data = self.generate_sample_data(symbol, days)

        # Step 2: Calculate EMAs
        data_with_ema = self.calculate_ema(data)

        # Step 3: Detect crossovers with improved parameters
        data_with_signals = self.detect_crossovers(
            data_with_ema,
            min_separation_minutes=min_separation,
            confirmation_periods=confirmation_periods
        )

        # Step 4: Validate signals
        self.validate_signals(data_with_signals)

        # Step 5: Save data to CSV files
        candle_file = self.save_candle_data(data_with_signals)
        signals_file = self.save_signals_data(data_with_signals)

        # Step 6: Display summary
        self.display_summary(data_with_signals)

        print("=" * 50)
        print("✅ Analysis completed successfully!")

        return candle_file, signals_file

    def display_summary(self, data: pd.DataFrame) -> None:
        """
        Display analysis summary

        Args:
            data: DataFrame with complete analysis data
        """
        print("\n📊 ANALYSIS SUMMARY")
        print("-" * 30)

        total_records = len(data)
        buy_signals = data['buy_signal'].sum()
        sell_signals = data['sell_signal'].sum()

        print(f"Total data points: {total_records}")
        print(f"Buy signals: {buy_signals}")
        print(f"Sell signals: {sell_signals}")
        print(f"Signal frequency: {((buy_signals + sell_signals) / total_records * 100):.2f}%")

        if buy_signals > 0 or sell_signals > 0:
            print("\n🎯 RECENT SIGNALS:")
            recent_signals = data[data['signal'].isin(['BUY', 'SELL'])].tail(5)
            for _, row in recent_signals.iterrows():
                signal_emoji = "📈" if row['signal'] == 'BUY' else "📉"
                print(f"  {signal_emoji} {row['timestamp']}: {row['signal']} at {row['close']:.2f}")


def main():
    """
    Main function to run the EMA crossover analysis
    """
    print("🎯 EMA Crossover Analysis Tool")
    print("=" * 50)

    # Load configuration
    if CONFIG_AVAILABLE:
        config = get_config()

        # Validate configuration
        is_valid, error_msg = validate_config()
        if not is_valid:
            print(f"❌ Configuration error: {error_msg}")
            return

        # Print configuration
        print_config()

        # Extract configuration values
        CLIENT_CODE = config['dhan_client_code']
        TOKEN_ID = config['dhan_token_id']
        SYMBOL = config['symbol']
        EXCHANGE = config['exchange']
        USE_SAMPLE_DATA = config['use_sample_data']
        TRADING_DAYS = config['trading_days']
        MIN_SEPARATION_MINUTES = config['min_separation_minutes']
        CONFIRMATION_PERIODS = config['confirmation_periods']
    else:
        # Fallback configuration
        print("📋 Using default configuration:")
        CLIENT_CODE = None
        TOKEN_ID = None
        SYMBOL = "NIFTY"
        EXCHANGE = "INDEX"
        USE_SAMPLE_DATA = False
        TRADING_DAYS = 2
        MIN_SEPARATION_MINUTES = 10
        CONFIRMATION_PERIODS = 2

        print(f"   Symbol: {SYMBOL}")
        print(f"   Exchange: {EXCHANGE}")
        print(f"   Trading Days: {TRADING_DAYS}")
        print(f"   Use Sample Data: {USE_SAMPLE_DATA}")
        print(f"   Min Separation: {MIN_SEPARATION_MINUTES} minutes")
        print(f"   Confirmation Periods: {CONFIRMATION_PERIODS}")
        print()

    try:
        # Initialize analyzer
        analyzer = EMACrossoverAnalyzer(CLIENT_CODE, TOKEN_ID)

        # Run analysis with improved parameters
        candle_file, signals_file = analyzer.run_analysis(
            symbol=SYMBOL,
            exchange=EXCHANGE,
            use_sample_data=USE_SAMPLE_DATA,
            days=TRADING_DAYS,
            min_separation=MIN_SEPARATION_MINUTES,
            confirmation_periods=CONFIRMATION_PERIODS
        )

        print(f"\n📁 Output files created:")
        print(f"  📊 Candle data: {candle_file}")
        print(f"  🎯 Signals data: {signals_file}")

    except Exception as e:
        print(f"❌ Error during analysis: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
