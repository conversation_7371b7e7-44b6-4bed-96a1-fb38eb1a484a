INFO:2024-12-18 11:29:20,736:MainThread:<PERSON>han.py  started system
INFO:2024-12-18 11:29:20,736:MainThread:STARTED THE PROGRAM
INFO:2024-12-18 11:30:18,879:MainThread:<PERSON>han.py  started system
INFO:2024-12-18 11:30:18,879:MainThread:STARTED THE PROGRAM
INFO:2024-12-18 11:43:54,974:MainThread:Dhan.py  started system
INFO:2024-12-18 11:43:54,974:MainThread:STARTED THE PROGRAM
INFO:2024-12-18 11:44:35,694:MainThread:Dhan.py  started system
INFO:2024-12-18 11:44:35,694:MainThread:STARTED THE PROGRAM
INFO:2024-12-18 11:46:52,915:MainThread:Dhan.py  started system
INFO:2024-12-18 11:46:52,915:MainThread:STARTED THE PROGRAM
INFO:2024-12-18 11:47:56,850:MainThread:Dhan.py  started system
INFO:2024-12-18 11:47:56,850:MainThread:STARTED THE PROGRAM
INFO:2024-12-18 12:37:32,570:MainThread:Dhan.py  started system
INFO:2024-12-18 12:37:32,570:MainThread:STARTED THE PROGRAM
INFO:2024-12-18 13:01:13,991:MainThread:Dhan.py  started system
INFO:2024-12-18 13:01:13,991:MainThread:STARTED THE PROGRAM
INFO:2024-12-18 13:02:07,286:MainThread:Dhan.py  started system
INFO:2024-12-18 13:02:07,287:MainThread:STARTED THE PROGRAM
ERROR:2024-12-18 13:02:16,391:MainThread:Exception in Getting OHLC data as Check the Tradingsymbol or Exchange
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\Dhan_Tradehull\Dhan_Tradehull.py", line 426, in get_historical_data
    raise Exception("Check the Tradingsymbol or Exchange")
Exception: Check the Tradingsymbol or Exchange
INFO:2024-12-18 13:02:31,133:MainThread:Dhan.py  started system
INFO:2024-12-18 13:02:31,134:MainThread:STARTED THE PROGRAM
INFO:2024-12-18 13:05:04,645:MainThread:Dhan.py  started system
INFO:2024-12-18 13:05:04,645:MainThread:STARTED THE PROGRAM
ERROR:2024-12-18 15:27:59,407:MainThread:Exception in get quote function as 'Tradehull' object has no attribute 'get_data_for_single_script'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\cmd.py", line 214, in onecmd
    func = getattr(self, 'do_' + cmd)
AttributeError: 'Pdb' object has no attribute 'do_tsl'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\Dhan_Tradehull\Dhan_Tradehull.py", line 656, in get_quote
    response = self.get_data_for_single_script(names)
AttributeError: 'Tradehull' object has no attribute 'get_data_for_single_script'
